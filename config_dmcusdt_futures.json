{"symbol": "DMCUSDT", "base_asset": "DMC", "quote_asset": "USDT", "trading_mode": "futures", "strategy": "LONG", "leverage": 1, "margin_type": "CROSS", "grid_config": {"lower_price": 0.004692, "upper_price": 0.005624, "grid_levels": 31, "total_investment": 23.0, "grid_quantity": 0.74193548}, "position_config": {"direction": "LONG", "leverage": 1, "margin_type": "CROSS", "initial_base_amount": 0.0, "initial_quote_amount": 23.0, "max_position_size": 23.0, "position_side": "LONG"}, "risk_management": {"max_drawdown_percent": 15.0, "stop_loss_percent": 10.0, "take_profit_percent": 20.0, "max_exposure_percent": 95.0, "enable_trailing_up": true, "enable_trailing_down": true, "trailing_up_percent": 2.0, "trailing_down_percent": 1.5}, "grid_parameters": {"grid_spacing_percent": 3.0, "rebalance_threshold": 0.05, "min_order_size": 0.1, "max_order_size": 5.0, "order_timeout_seconds": 30}, "trading_settings": {"enable_auto_rebalance": true, "rebalance_interval_minutes": 60, "enable_dynamic_grid": false, "price_update_interval_seconds": 5, "order_check_interval_seconds": 10}, "logging": {"log_level": "INFO", "enable_trade_logging": true, "enable_pnl_logging": true, "log_file_path": "logs/dmcusdt_futures.log"}, "notifications": {"enable_notifications": false, "webhook_url": "", "notification_events": ["trade_executed", "position_opened", "position_closed", "error"]}, "advanced_settings": {"enable_hedge_mode": false, "reduce_only_orders": false, "time_in_force": "GTC", "order_type": "LIMIT", "price_precision": 6, "quantity_precision": 0}}
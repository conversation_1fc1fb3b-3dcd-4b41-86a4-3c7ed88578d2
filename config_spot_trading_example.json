{"is_testnet": true, "live_api_url": "https://fapi.binance.com", "live_ws_url": "wss://fstream.binance.com", "testnet_api_url": "https://testnet.binancefuture.com", "testnet_ws_url": "wss://stream.binancefuture.com", "symbol": "BTCUSDT", "grid_spacing": 0.01, "grid_quantity": 0.001, "min_notional_value": 10.0, "initial_investment": 1000.0, "leverage": 1, "margin_type": "CROSSED", "hedge_mode": false, "grid_count": 50, "active_orders_count": 5, "return_rate": 0.1, "wallet_exposure_limit": 0.8, "retry_attempts": 5, "retry_initial_delay_ms": 1000, "enable_trailing_up": true, "enable_trailing_down": true, "trailing_up_distance": 0.02, "trailing_down_distance": 0.015, "trailing_type": "percentage", "_comment_trading_mode": "=== SPOT TRADING CONFIGURATION ===", "trading_mode": "spot", "_comment_spot_explanation": "Spot trading mode configuration", "_comment_spot_assets": "=== SPOT ASSET CONFIGURATION ===", "base_asset": "BTC", "quote_asset": "USDT", "initial_base_amount": 0.05, "initial_quote_amount": 500.0, "_comment_assets_explanation": "base_asset: BTC, quote_asset: USDT, initial amounts for grid trading", "_comment_spot_urls": "=== SPOT API CONFIGURATION ===", "spot_api_url": "https://api.binance.com", "spot_ws_url": "wss://stream.binance.com", "spot_testnet_api_url": "https://testnet.binance.vision", "spot_testnet_ws_url": "wss://testnet.binance.vision", "_comment_spot_urls_explanation": "Binance Spot API and WebSocket URLs", "log": {"level": "info", "output": "both", "file": "logs/grid-bot-spot.log", "max_size": 10, "max_backups": 5, "max_age": 30, "compress": false}, "websocket_ping_interval_sec": 30, "websocket_pong_timeout_sec": 75}
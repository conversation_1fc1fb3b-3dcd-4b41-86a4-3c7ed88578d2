package reporting

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/yudaprama/grid-trading-bot/internal/logger"
)

// PnLTracker tracks profit and loss for spot grid trading
type PnLTracker struct {
	mutex sync.RWMutex
	
	// Initial state
	InitialBaseAmount  float64 `json:"initial_base_amount"`
	InitialQuoteAmount float64 `json:"initial_quote_amount"`
	InitialEntryPrice  float64 `json:"initial_entry_price"`
	InitialPortfolioValue float64 `json:"initial_portfolio_value"`
	StartTime          time.Time `json:"start_time"`
	
	// Current state
	CurrentBaseAmount  float64 `json:"current_base_amount"`
	CurrentQuoteAmount float64 `json:"current_quote_amount"`
	CurrentPrice       float64 `json:"current_price"`
	CurrentPortfolioValue float64 `json:"current_portfolio_value"`
	LastUpdateTime     time.Time `json:"last_update_time"`
	
	// Trading metrics
	CompletedTrades    []CompletedGridTrade `json:"completed_trades"`
	TotalRealizedPnL   float64 `json:"total_realized_pnl"`
	TotalUnrealizedPnL float64 `json:"total_unrealized_pnl"`
	TotalFees          float64 `json:"total_fees"`
	NetPnL             float64 `json:"net_pnl"`
	
	// Performance metrics
	TotalTradeCount    int     `json:"total_trade_count"`
	WinningTrades      int     `json:"winning_trades"`
	LosingTrades       int     `json:"losing_trades"`
	AverageProfitPerTrade float64 `json:"average_profit_per_trade"`
	WinRate            float64 `json:"win_rate"`
	
	// Configuration
	Symbol     string `json:"symbol"`
	BaseAsset  string `json:"base_asset"`
	QuoteAsset string `json:"quote_asset"`
	
	// Reporting
	ReportFile string `json:"report_file"`
}

// CompletedGridTrade represents a completed buy-sell pair in grid trading
type CompletedGridTrade struct {
	TradeID       string    `json:"trade_id"`
	BuyOrderID    int64     `json:"buy_order_id"`
	SellOrderID   int64     `json:"sell_order_id"`
	BuyPrice      float64   `json:"buy_price"`
	SellPrice     float64   `json:"sell_price"`
	Quantity      float64   `json:"quantity"`
	BuyTime       time.Time `json:"buy_time"`
	SellTime      time.Time `json:"sell_time"`
	GrossPnL      float64   `json:"gross_pnl"`
	BuyFee        float64   `json:"buy_fee"`
	SellFee       float64   `json:"sell_fee"`
	NetPnL        float64   `json:"net_pnl"`
	PnLPercentage float64   `json:"pnl_percentage"`
	HoldDuration  time.Duration `json:"hold_duration"`
}

// PnLSnapshot represents a point-in-time P&L summary
type PnLSnapshot struct {
	Timestamp         time.Time `json:"timestamp"`
	CurrentPrice      float64   `json:"current_price"`
	BaseHoldings      float64   `json:"base_holdings"`
	QuoteHoldings     float64   `json:"quote_holdings"`
	PortfolioValue    float64   `json:"portfolio_value"`
	UnrealizedPnL     float64   `json:"unrealized_pnl"`
	RealizedPnL       float64   `json:"realized_pnl"`
	TotalFees         float64   `json:"total_fees"`
	NetPnL            float64   `json:"net_pnl"`
	PnLPercentage     float64   `json:"pnl_percentage"`
	CompletedTrades   int       `json:"completed_trades"`
	ActiveOrders      int       `json:"active_orders"`
}

// NewPnLTracker creates a new P&L tracker
func NewPnLTracker(symbol, baseAsset, quoteAsset string, initialBaseAmount, initialQuoteAmount, entryPrice float64) *PnLTracker {
	initialPortfolioValue := (initialBaseAmount * entryPrice) + initialQuoteAmount
	
	tracker := &PnLTracker{
		InitialBaseAmount:     initialBaseAmount,
		InitialQuoteAmount:    initialQuoteAmount,
		InitialEntryPrice:     entryPrice,
		InitialPortfolioValue: initialPortfolioValue,
		StartTime:             time.Now(),
		
		CurrentBaseAmount:     initialBaseAmount,
		CurrentQuoteAmount:    initialQuoteAmount,
		CurrentPrice:          entryPrice,
		CurrentPortfolioValue: initialPortfolioValue,
		LastUpdateTime:        time.Now(),
		
		CompletedTrades:    make([]CompletedGridTrade, 0),
		TotalRealizedPnL:   0,
		TotalUnrealizedPnL: 0,
		TotalFees:          0,
		NetPnL:             0,
		
		TotalTradeCount:       0,
		WinningTrades:         0,
		LosingTrades:          0,
		AverageProfitPerTrade: 0,
		WinRate:               0,
		
		Symbol:     symbol,
		BaseAsset:  baseAsset,
		QuoteAsset: quoteAsset,
		
		ReportFile: fmt.Sprintf("logs/pnl_report_%s_%s.json", symbol, time.Now().Format("20060102_150405")),
	}
	
	logger.S().Infof("P&L Tracker initialized for %s", symbol)
	logger.S().Infof("Initial Portfolio: %.8f %s + %.2f %s = %.2f %s (@ %.2f)", 
		initialBaseAmount, baseAsset, initialQuoteAmount, quoteAsset, 
		initialPortfolioValue, quoteAsset, entryPrice)
	
	return tracker
}

// UpdateCurrentState updates the current market state
func (p *PnLTracker) UpdateCurrentState(baseAmount, quoteAmount, currentPrice float64) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	p.CurrentBaseAmount = baseAmount
	p.CurrentQuoteAmount = quoteAmount
	p.CurrentPrice = currentPrice
	p.CurrentPortfolioValue = (baseAmount * currentPrice) + quoteAmount
	p.LastUpdateTime = time.Now()
	
	// Calculate unrealized P&L
	p.TotalUnrealizedPnL = p.CurrentPortfolioValue - p.InitialPortfolioValue
	
	// Calculate net P&L
	p.NetPnL = p.TotalRealizedPnL + p.TotalUnrealizedPnL - p.TotalFees
}

// RecordCompletedTrade records a completed grid trade
func (p *PnLTracker) RecordCompletedTrade(trade CompletedGridTrade) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	
	// Add to completed trades
	p.CompletedTrades = append(p.CompletedTrades, trade)
	
	// Update metrics
	p.TotalTradeCount++
	p.TotalRealizedPnL += trade.NetPnL
	p.TotalFees += trade.BuyFee + trade.SellFee
	
	if trade.NetPnL > 0 {
		p.WinningTrades++
	} else {
		p.LosingTrades++
	}
	
	// Calculate averages
	p.AverageProfitPerTrade = p.TotalRealizedPnL / float64(p.TotalTradeCount)
	p.WinRate = float64(p.WinningTrades) / float64(p.TotalTradeCount) * 100
	
	// Update net P&L
	p.NetPnL = p.TotalRealizedPnL + p.TotalUnrealizedPnL - p.TotalFees
	
	logger.S().Infof("Completed Grid Trade: %s -> %s, Quantity: %.8f, Net P&L: %.4f %s", 
		fmt.Sprintf("%.2f", trade.BuyPrice), fmt.Sprintf("%.2f", trade.SellPrice), 
		trade.Quantity, trade.NetPnL, p.QuoteAsset)
}

// GetCurrentSnapshot returns a current P&L snapshot
func (p *PnLTracker) GetCurrentSnapshot() PnLSnapshot {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	
	pnlPercentage := 0.0
	if p.InitialPortfolioValue > 0 {
		pnlPercentage = (p.NetPnL / p.InitialPortfolioValue) * 100
	}
	
	return PnLSnapshot{
		Timestamp:       time.Now(),
		CurrentPrice:    p.CurrentPrice,
		BaseHoldings:    p.CurrentBaseAmount,
		QuoteHoldings:   p.CurrentQuoteAmount,
		PortfolioValue:  p.CurrentPortfolioValue,
		UnrealizedPnL:   p.TotalUnrealizedPnL,
		RealizedPnL:     p.TotalRealizedPnL,
		TotalFees:       p.TotalFees,
		NetPnL:          p.NetPnL,
		PnLPercentage:   pnlPercentage,
		CompletedTrades: p.TotalTradeCount,
		ActiveOrders:    0, // Will be updated by caller
	}
}

// PrintCurrentStatus prints the current P&L status to console
func (p *PnLTracker) PrintCurrentStatus(activeOrders int) {
	snapshot := p.GetCurrentSnapshot()
	snapshot.ActiveOrders = activeOrders
	
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Printf("📊 REAL-TIME P&L REPORT - %s\n", p.Symbol)
	fmt.Println(strings.Repeat("=", 80))
	
	// Current Position Summary
	fmt.Printf("🏦 CURRENT POSITION SUMMARY:\n")
	fmt.Printf("   Current Price:      %s %.2f\n", p.QuoteAsset, snapshot.CurrentPrice)
	fmt.Printf("   %s Holdings:       %.8f (Initial: %.8f) [%+.8f]\n", 
		p.BaseAsset, snapshot.BaseHoldings, p.InitialBaseAmount, 
		snapshot.BaseHoldings - p.InitialBaseAmount)
	fmt.Printf("   %s Balance:        %.2f (Initial: %.2f) [%+.2f]\n", 
		p.QuoteAsset, snapshot.QuoteHoldings, p.InitialQuoteAmount, 
		snapshot.QuoteHoldings - p.InitialQuoteAmount)
	fmt.Printf("   Portfolio Value:    %s %.2f (Initial: %.2f)\n", 
		p.QuoteAsset, snapshot.PortfolioValue, p.InitialPortfolioValue)
	
	// P&L Summary
	fmt.Printf("\n💰 PROFIT & LOSS SUMMARY:\n")
	fmt.Printf("   Unrealized P&L:     %s %+.4f\n", p.QuoteAsset, snapshot.UnrealizedPnL)
	fmt.Printf("   Realized P&L:       %s %+.4f\n", p.QuoteAsset, snapshot.RealizedPnL)
	fmt.Printf("   Total Fees:         %s %.4f\n", p.QuoteAsset, snapshot.TotalFees)
	fmt.Printf("   Net P&L:            %s %+.4f (%+.2f%%)\n", 
		p.QuoteAsset, snapshot.NetPnL, snapshot.PnLPercentage)
	
	// Trading Performance
	fmt.Printf("\n📈 TRADING PERFORMANCE:\n")
	fmt.Printf("   Completed Trades:   %d\n", snapshot.CompletedTrades)
	fmt.Printf("   Active Orders:      %d\n", snapshot.ActiveOrders)
	if p.TotalTradeCount > 0 {
		fmt.Printf("   Winning Trades:     %d (%.1f%%)\n", p.WinningTrades, p.WinRate)
		fmt.Printf("   Losing Trades:      %d (%.1f%%)\n", p.LosingTrades, 100-p.WinRate)
		fmt.Printf("   Avg Profit/Trade:   %s %.4f\n", p.QuoteAsset, p.AverageProfitPerTrade)
	}
	
	// Runtime Info
	runtime := time.Since(p.StartTime)
	fmt.Printf("\n⏱️  RUNTIME INFO:\n")
	fmt.Printf("   Bot Runtime:        %v\n", runtime.Truncate(time.Second))
	fmt.Printf("   Last Update:        %s\n", snapshot.Timestamp.Format("15:04:05"))
	
	fmt.Println(strings.Repeat("=", 80))
}

// SaveReportToFile saves the current P&L report to a JSON file
func (p *PnLTracker) SaveReportToFile() error {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	
	// Create logs directory if it doesn't exist
	if err := os.MkdirAll("logs", 0755); err != nil {
		return fmt.Errorf("failed to create logs directory: %v", err)
	}
	
	// Create detailed report
	report := map[string]interface{}{
		"summary": p.GetCurrentSnapshot(),
		"initial_state": map[string]interface{}{
			"base_amount":      p.InitialBaseAmount,
			"quote_amount":     p.InitialQuoteAmount,
			"entry_price":      p.InitialEntryPrice,
			"portfolio_value":  p.InitialPortfolioValue,
			"start_time":       p.StartTime,
		},
		"current_state": map[string]interface{}{
			"base_amount":      p.CurrentBaseAmount,
			"quote_amount":     p.CurrentQuoteAmount,
			"current_price":    p.CurrentPrice,
			"portfolio_value":  p.CurrentPortfolioValue,
			"last_update":      p.LastUpdateTime,
		},
		"performance_metrics": map[string]interface{}{
			"total_trades":         p.TotalTradeCount,
			"winning_trades":       p.WinningTrades,
			"losing_trades":        p.LosingTrades,
			"win_rate":             p.WinRate,
			"average_profit":       p.AverageProfitPerTrade,
			"total_realized_pnl":   p.TotalRealizedPnL,
			"total_unrealized_pnl": p.TotalUnrealizedPnL,
			"total_fees":           p.TotalFees,
			"net_pnl":              p.NetPnL,
		},
		"completed_trades": p.CompletedTrades,
		"generated_at":     time.Now(),
	}
	
	// Write to file
	data, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal report: %v", err)
	}
	
	if err := os.WriteFile(p.ReportFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write report file: %v", err)
	}
	
	return nil
}

// GetDetailedReport returns a detailed text report
func (p *PnLTracker) GetDetailedReport() string {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	
	snapshot := p.GetCurrentSnapshot()
	runtime := time.Since(p.StartTime)
	
	report := fmt.Sprintf(`
DETAILED P&L REPORT - %s
Generated: %s
Runtime: %v

INITIAL STATE:
- Base Amount: %.8f %s
- Quote Amount: %.2f %s  
- Entry Price: %.2f %s
- Portfolio Value: %.2f %s

CURRENT STATE:
- Current Price: %.2f %s
- Base Holdings: %.8f %s (%+.8f)
- Quote Balance: %.2f %s (%+.2f)
- Portfolio Value: %.2f %s

PROFIT & LOSS:
- Unrealized P&L: %+.4f %s
- Realized P&L: %+.4f %s
- Total Fees: %.4f %s
- Net P&L: %+.4f %s (%+.2f%%)

TRADING PERFORMANCE:
- Total Trades: %d
- Winning Trades: %d (%.1f%%)
- Losing Trades: %d (%.1f%%)
- Average Profit per Trade: %.4f %s
- Total Fees Paid: %.4f %s

RECENT TRADES:
`, 
		p.Symbol, time.Now().Format("2006-01-02 15:04:05"), runtime.Truncate(time.Second),
		p.InitialBaseAmount, p.BaseAsset, p.InitialQuoteAmount, p.QuoteAsset,
		p.InitialEntryPrice, p.QuoteAsset, p.InitialPortfolioValue, p.QuoteAsset,
		snapshot.CurrentPrice, p.QuoteAsset,
		snapshot.BaseHoldings, p.BaseAsset, snapshot.BaseHoldings - p.InitialBaseAmount,
		snapshot.QuoteHoldings, p.QuoteAsset, snapshot.QuoteHoldings - p.InitialQuoteAmount,
		snapshot.PortfolioValue, p.QuoteAsset,
		snapshot.UnrealizedPnL, p.QuoteAsset,
		snapshot.RealizedPnL, p.QuoteAsset,
		snapshot.TotalFees, p.QuoteAsset,
		snapshot.NetPnL, p.QuoteAsset, snapshot.PnLPercentage,
		snapshot.CompletedTrades,
		p.WinningTrades, p.WinRate,
		p.LosingTrades, 100-p.WinRate,
		p.AverageProfitPerTrade, p.QuoteAsset,
		snapshot.TotalFees, p.QuoteAsset)
	
	// Add recent trades
	recentCount := 5
	if len(p.CompletedTrades) < recentCount {
		recentCount = len(p.CompletedTrades)
	}
	
	if recentCount > 0 {
		for i := len(p.CompletedTrades) - recentCount; i < len(p.CompletedTrades); i++ {
			trade := p.CompletedTrades[i]
			report += fmt.Sprintf("- %s: %.2f -> %.2f, Qty: %.8f, P&L: %+.4f %s\n",
				trade.SellTime.Format("15:04:05"), trade.BuyPrice, trade.SellPrice,
				trade.Quantity, trade.NetPnL, p.QuoteAsset)
		}
	} else {
		report += "- No completed trades yet\n"
	}
	
	return report
}

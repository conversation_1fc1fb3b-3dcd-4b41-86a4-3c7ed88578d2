package reporting

import (
	"context"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/yudaprama/grid-trading-bot/internal/logger"
	"github.com/yudaprama/grid-trading-bot/internal/models"
	"github.com/yudaprama/grid-trading-bot/internal/trading"
)

// PnLReporter provides real-time P&L reporting services
type PnLReporter struct {
	tracker      *PnLTracker
	tradingMode  trading.TradingMode
	exchange     trading.ModeAwareExchange
	config       *models.Config
	
	// Reporting configuration
	reportInterval   time.Duration
	consoleReporting bool
	fileReporting    bool
	
	// Control
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
	isRunning  bool
	mutex      sync.RWMutex
	
	// Order tracking for P&L calculation
	pendingOrders map[int64]*PendingOrder
	orderMutex    sync.RWMutex
}

// PendingOrder tracks orders for P&L calculation
type PendingOrder struct {
	OrderID   int64     `json:"order_id"`
	Side      string    `json:"side"`
	Price     float64   `json:"price"`
	Quantity  float64   `json:"quantity"`
	GridID    int       `json:"grid_id"`
	PlacedAt  time.Time `json:"placed_at"`
}

// NewPnLReporter creates a new P&L reporter
func NewPnLReporter(tracker *PnLTracker, tradingMode trading.TradingMode, exchange trading.ModeAwareExchange, config *models.Config) *PnLReporter {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &PnLReporter{
		tracker:          tracker,
		tradingMode:      tradingMode,
		exchange:         exchange,
		config:           config,
		reportInterval:   30 * time.Second, // Report every 30 seconds
		consoleReporting: true,
		fileReporting:    true,
		ctx:              ctx,
		cancel:           cancel,
		pendingOrders:    make(map[int64]*PendingOrder),
	}
}

// Start begins the real-time P&L reporting
func (r *PnLReporter) Start() error {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	if r.isRunning {
		return fmt.Errorf("P&L reporter is already running")
	}
	
	r.isRunning = true
	
	// Start reporting goroutines
	r.wg.Add(3)
	go r.periodicReporting()
	go r.balanceMonitoring()
	go r.orderMonitoring()
	
	logger.S().Info("P&L Reporter started successfully")
	return nil
}

// Stop stops the P&L reporting
func (r *PnLReporter) Stop() {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	if !r.isRunning {
		return
	}
	
	r.cancel()
	r.wg.Wait()
	r.isRunning = false
	
	// Generate final report
	r.generateFinalReport()
	
	logger.S().Info("P&L Reporter stopped")
}

// periodicReporting handles periodic console and file reporting
func (r *PnLReporter) periodicReporting() {
	defer r.wg.Done()
	
	ticker := time.NewTicker(r.reportInterval)
	defer ticker.Stop()
	
	// Initial report
	r.updateAndReport()
	
	for {
		select {
		case <-r.ctx.Done():
			return
		case <-ticker.C:
			r.updateAndReport()
		}
	}
}

// balanceMonitoring monitors account balances for P&L calculation
func (r *PnLReporter) balanceMonitoring() {
	defer r.wg.Done()
	
	ticker := time.NewTicker(10 * time.Second) // Check balances every 10 seconds
	defer ticker.Stop()
	
	for {
		select {
		case <-r.ctx.Done():
			return
		case <-ticker.C:
			r.updateBalances()
		}
	}
}

// orderMonitoring monitors order status for completed trade detection
func (r *PnLReporter) orderMonitoring() {
	defer r.wg.Done()
	
	ticker := time.NewTicker(5 * time.Second) // Check orders every 5 seconds
	defer ticker.Stop()
	
	for {
		select {
		case <-r.ctx.Done():
			return
		case <-ticker.C:
			r.checkCompletedTrades()
		}
	}
}

// updateAndReport updates P&L data and generates reports
func (r *PnLReporter) updateAndReport() {
	// Update current state
	r.updateBalances()
	
	// Get active orders count
	activeOrders := r.getActiveOrdersCount()
	
	// Console reporting
	if r.consoleReporting {
		r.tracker.PrintCurrentStatus(activeOrders)
	}
	
	// File reporting
	if r.fileReporting {
		if err := r.tracker.SaveReportToFile(); err != nil {
			logger.S().Errorf("Failed to save P&L report to file: %v", err)
		}
	}
}

// updateBalances updates current balance information
func (r *PnLReporter) updateBalances() {
	accountState, err := r.tradingMode.GetAccountState()
	if err != nil {
		logger.S().Errorf("Failed to get account state for P&L update: %v", err)
		return
	}
	
	// Get current price
	currentPrice, err := r.exchange.GetPrice(r.config.Symbol)
	if err != nil {
		logger.S().Errorf("Failed to get current price for P&L update: %v", err)
		return
	}
	
	// Update tracker
	r.tracker.UpdateCurrentState(accountState.BaseBalance, accountState.QuoteBalance, currentPrice)
}

// getActiveOrdersCount returns the number of active orders
func (r *PnLReporter) getActiveOrdersCount() int {
	orders, err := r.exchange.GetOpenOrders(r.config.Symbol)
	if err != nil {
		logger.S().Errorf("Failed to get open orders count: %v", err)
		return 0
	}
	return len(orders)
}

// checkCompletedTrades checks for completed trades and updates P&L
func (r *PnLReporter) checkCompletedTrades() {
	orders, err := r.exchange.GetOpenOrders(r.config.Symbol)
	if err != nil {
		logger.S().Errorf("Failed to get open orders for trade monitoring: %v", err)
		return
	}
	
	// Create map of current active orders
	activeOrderIDs := make(map[int64]bool)
	for _, order := range orders {
		activeOrderIDs[order.OrderId] = true
	}
	
	// Check for completed orders
	r.orderMutex.Lock()
	for orderID, pendingOrder := range r.pendingOrders {
		if !activeOrderIDs[orderID] {
			// Order is no longer active, it might be filled
			r.handlePotentiallyCompletedOrder(orderID, pendingOrder)
			delete(r.pendingOrders, orderID)
		}
	}
	r.orderMutex.Unlock()
}

// handlePotentiallyCompletedOrder handles a potentially completed order
func (r *PnLReporter) handlePotentiallyCompletedOrder(orderID int64, pendingOrder *PendingOrder) {
	// Get order status to confirm it was filled
	orderStatus, err := r.exchange.GetOrderStatus(r.config.Symbol, orderID)
	if err != nil {
		logger.S().Errorf("Failed to get order status for %d: %v", orderID, err)
		return
	}
	
	if orderStatus.Status == "FILLED" {
		// Try to match with opposite side for completed grid trade
		r.tryMatchGridTrade(orderStatus, pendingOrder)
	}
}

// tryMatchGridTrade attempts to match a filled order with its opposite for a completed grid trade
func (r *PnLReporter) tryMatchGridTrade(filledOrder *models.Order, pendingOrder *PendingOrder) {
	// This is a simplified implementation
	// In a full implementation, you would maintain a more sophisticated matching system

	// For now, we'll record individual trades and let the P&L tracker handle the calculations
	// In a more sophisticated system, you would match buy/sell pairs

	logger.S().Infof("Order filled: %s %s @ %s, Quantity: %s",
		filledOrder.Side, r.config.Symbol, filledOrder.Price, filledOrder.OrigQty)
}

// RegisterOrder registers a new order for P&L tracking
func (r *PnLReporter) RegisterOrder(orderID int64, side string, price, quantity float64, gridID int) {
	r.orderMutex.Lock()
	defer r.orderMutex.Unlock()
	
	r.pendingOrders[orderID] = &PendingOrder{
		OrderID:  orderID,
		Side:     side,
		Price:    price,
		Quantity: quantity,
		GridID:   gridID,
		PlacedAt: time.Now(),
	}
}

// RecordCompletedGridTrade records a completed grid trade
func (r *PnLReporter) RecordCompletedGridTrade(buyOrderID, sellOrderID int64, buyPrice, sellPrice, quantity, buyFee, sellFee float64) {
	trade := CompletedGridTrade{
		TradeID:       fmt.Sprintf("%d_%d", buyOrderID, sellOrderID),
		BuyOrderID:    buyOrderID,
		SellOrderID:   sellOrderID,
		BuyPrice:      buyPrice,
		SellPrice:     sellPrice,
		Quantity:      quantity,
		BuyTime:       time.Now(), // Simplified - should get actual times
		SellTime:      time.Now(),
		GrossPnL:      (sellPrice - buyPrice) * quantity,
		BuyFee:        buyFee,
		SellFee:       sellFee,
		NetPnL:        (sellPrice - buyPrice) * quantity - buyFee - sellFee,
		PnLPercentage: ((sellPrice - buyPrice) / buyPrice) * 100,
		HoldDuration:  0, // Simplified
	}
	
	r.tracker.RecordCompletedTrade(trade)
}

// GetCurrentSnapshot returns current P&L snapshot
func (r *PnLReporter) GetCurrentSnapshot() PnLSnapshot {
	activeOrders := r.getActiveOrdersCount()
	snapshot := r.tracker.GetCurrentSnapshot()
	snapshot.ActiveOrders = activeOrders
	return snapshot
}

// PrintCurrentStatus prints current P&L status
func (r *PnLReporter) PrintCurrentStatus() {
	activeOrders := r.getActiveOrdersCount()
	r.tracker.PrintCurrentStatus(activeOrders)
}

// generateFinalReport generates a final comprehensive report
func (r *PnLReporter) generateFinalReport() {
	finalReport := r.tracker.GetDetailedReport()
	
	// Save to file
	finalReportFile := fmt.Sprintf("logs/final_pnl_report_%s_%s.txt", 
		r.config.Symbol, time.Now().Format("20060102_150405"))
	
	if err := os.WriteFile(finalReportFile, []byte(finalReport), 0644); err != nil {
		logger.S().Errorf("Failed to save final P&L report: %v", err)
	} else {
		logger.S().Infof("Final P&L report saved to: %s", finalReportFile)
	}
	
	// Print to console
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("📋 FINAL P&L REPORT")
	fmt.Println(strings.Repeat("=", 80))
	fmt.Print(finalReport)
	fmt.Println(strings.Repeat("=", 80))
}

// SetReportInterval sets the reporting interval
func (r *PnLReporter) SetReportInterval(interval time.Duration) {
	r.reportInterval = interval
}

// EnableConsoleReporting enables/disables console reporting
func (r *PnLReporter) EnableConsoleReporting(enabled bool) {
	r.consoleReporting = enabled
}

// EnableFileReporting enables/disables file reporting
func (r *PnLReporter) EnableFileReporting(enabled bool) {
	r.fileReporting = enabled
}

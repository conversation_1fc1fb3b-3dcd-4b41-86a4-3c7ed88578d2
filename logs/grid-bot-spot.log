2025-07-23T20:14:38.288+0700	[34mINFO[0m	bot/main.go:127	--- Starting Live Trading Mode ---
2025-07-23T20:14:38.289+0700	[34mINFO[0m	bot/main.go:149	Using Binance Spot testnet...
2025-07-23T20:14:38.289+0700	[34mINFO[0m	bot/main.go:184	Initialized Enhanced Spot trading exchange using go-binance SDK
2025-07-23T20:14:38.289+0700	[34mINFO[0m	bot/main.go:196	Initializing exchange settings...
2025-07-23T20:14:38.289+0700	[34mINFO[0m	bot/main.go:243	Spot trading mode - skipping futures-specific settings
2025-07-23T20:14:38.289+0700	[34mINFO[0m	bot/bot.go:93	Initialized spot trading mode
2025-07-23T20:14:38.656+0700	[34mINFO[0m	bot/bot.go:103	Successfully fetched and cached trading rules for BTCUSDT.
2025-07-23T20:14:38.656+0700	[34mINFO[0m	bot/bot.go:809	Starting bot...
2025-07-23T20:14:38.759+0700	[34mINFO[0m	bot/bot.go:674	Successfully obtained Listen Key: hyrt6VojLKlAfHRtdbFLEYGPorBs5wJMeL2acTslXPai5RdN5UVcWFI7QnEM
2025-07-23T20:14:38.759+0700	[31mFATAL[0m	bot/main.go:251	Failed to start bot: failed to connect to WebSocket on start: could not connect to WebSocket: WebSocket connection not implemented yet
2025-07-23T20:15:27.693+0700	[34mINFO[0m	bot/main.go:127	--- Starting Live Trading Mode ---
2025-07-23T20:15:27.693+0700	[34mINFO[0m	bot/main.go:149	Using Binance Spot testnet...
2025-07-23T20:15:27.693+0700	[34mINFO[0m	bot/main.go:184	Initialized Enhanced Spot trading exchange using go-binance SDK
2025-07-23T20:15:27.693+0700	[34mINFO[0m	bot/main.go:196	Initializing exchange settings...
2025-07-23T20:15:27.693+0700	[34mINFO[0m	bot/main.go:243	Spot trading mode - skipping futures-specific settings
2025-07-23T20:15:27.694+0700	[34mINFO[0m	bot/bot.go:93	Initialized spot trading mode
2025-07-23T20:15:27.980+0700	[34mINFO[0m	bot/bot.go:103	Successfully fetched and cached trading rules for BTCUSDT.
2025-07-23T20:15:27.980+0700	[34mINFO[0m	bot/bot.go:809	Starting bot...
2025-07-23T20:15:28.089+0700	[34mINFO[0m	bot/bot.go:674	Successfully obtained Listen Key: hyrt6VojLKlAfHRtdbFLEYGPorBs5wJMeL2acTslXPai5RdN5UVcWFI7QnEM
2025-07-23T20:15:28.484+0700	[31mFATAL[0m	bot/main.go:251	Failed to start bot: failed to connect to WebSocket on start: could not connect to WebSocket: failed to connect to spot WebSocket: websocket: bad handshake
2025-07-23T20:16:22.276+0700	[34mINFO[0m	bot/main.go:127	--- Starting Live Trading Mode ---
2025-07-23T20:16:22.277+0700	[34mINFO[0m	bot/main.go:149	Using Binance Spot testnet...
2025-07-23T20:16:22.277+0700	[34mINFO[0m	bot/main.go:184	Initialized Enhanced Spot trading exchange using go-binance SDK
2025-07-23T20:16:22.277+0700	[34mINFO[0m	bot/main.go:196	Initializing exchange settings...
2025-07-23T20:16:22.277+0700	[34mINFO[0m	bot/main.go:243	Spot trading mode - skipping futures-specific settings
2025-07-23T20:16:22.277+0700	[34mINFO[0m	bot/bot.go:93	Initialized spot trading mode
2025-07-23T20:16:22.601+0700	[34mINFO[0m	bot/bot.go:103	Successfully fetched and cached trading rules for BTCUSDT.
2025-07-23T20:16:22.601+0700	[34mINFO[0m	bot/bot.go:809	Starting bot...
2025-07-23T20:16:22.707+0700	[34mINFO[0m	bot/bot.go:674	Successfully obtained Listen Key: hyrt6VojLKlAfHRtdbFLEYGPorBs5wJMeL2acTslXPai5RdN5UVcWFI7QnEM
2025-07-23T20:16:23.230+0700	[34mINFO[0m	bot/bot.go:681	Successfully connected to user data stream WebSocket.
2025-07-23T20:16:23.230+0700	[34mINFO[0m	bot/bot.go:438	--- Starting new trading cycle ---
2025-07-23T20:16:23.230+0700	[34mINFO[0m	bot/bot.go:753	WebSocket message listener loop started.
2025-07-23T20:16:23.230+0700	[34mINFO[0m	bot/bot.go:1516	Core event processor started.
2025-07-23T20:16:23.335+0700	[34mINFO[0m	bot/bot.go:454	New cycle defined: Entry Price: 118064.8600, Reversion Price (Grid Top): 129871.3460
2025-07-23T20:16:23.335+0700	[34mINFO[0m	bot/bot.go:481	Successfully generated conceptual grid with 79 levels.
2025-07-23T20:16:23.335+0700	[34mINFO[0m	bot/bot.go:496	Calculated initial position quantity: 0.********
2025-07-23T20:16:23.335+0700	[33mWARN[0m	bot/bot.go:1048	Could not get positions to check exposure limit: GetPositions is only available in futures mode
2025-07-23T20:16:23.335+0700	[33mWARN[0m	bot/bot.go:499	Initial position blocked: wallet exposure limit would be exceeded.
2025-07-23T20:16:23.335+0700	[34mINFO[0m	bot/bot.go:519	Initial position confirmed, setting up grid orders...
2025-07-23T20:16:23.335+0700	[34mINFO[0m	bot/bot.go:261	Setting up LONG-optimized grid around price 118064.8600
2025-07-23T20:16:23.335+0700	[34mINFO[0m	bot/bot.go:1407	--- Setting up initial grid, center price: 118064.8600 ---
2025-07-23T20:16:23.335+0700	[34mINFO[0m	bot/bot.go:1432	Found closest pivot grid ID: 9 (Price: 118639.7100)
2025-07-23T20:16:23.336+0700	[33mWARN[0m	bot/bot.go:1048	Could not get positions to check exposure limit: GetPositions is only available in futures mode
2025-07-23T20:16:23.337+0700	[33mWARN[0m	bot/bot.go:1048	Could not get positions to check exposure limit: GetPositions is only available in futures mode
2025-07-23T20:16:23.337+0700	[33mWARN[0m	bot/bot.go:1048	Could not get positions to check exposure limit: GetPositions is only available in futures mode
2025-07-23T20:16:23.337+0700	[33mWARN[0m	bot/bot.go:1048	Could not get positions to check exposure limit: GetPositions is only available in futures mode
2025-07-23T20:16:23.337+0700	[33mWARN[0m	bot/bot.go:1048	Could not get positions to check exposure limit: GetPositions is only available in futures mode
2025-07-23T20:16:23.445+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922290, Price 122271.2900, Quantity 0.00099, GridID: 6. Waiting for confirmation...
2025-07-23T20:16:23.446+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922288, Price 123506.3500, Quantity 0.00099, GridID: 5. Waiting for confirmation...
2025-07-23T20:16:23.469+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922289, Price 119838.0900, Quantity 0.00099, GridID: 8. Waiting for confirmation...
2025-07-23T20:16:23.579+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922291, Price 121048.5800, Quantity 0.00099, GridID: 7. Waiting for confirmation...
2025-07-23T20:16:23.604+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922292, Price 124753.8900, Quantity 0.00099, GridID: 4. Waiting for confirmation...
2025-07-23T20:16:23.750+0700	[34mINFO[0m	bot/bot.go:581	Order 7922288 confirmed by exchange with status: NEW
2025-07-23T20:16:23.751+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922288, Price 123506.3500, Quantity 0.00099, GridID: 5
2025-07-23T20:16:23.772+0700	[34mINFO[0m	bot/bot.go:581	Order 7922290 confirmed by exchange with status: NEW
2025-07-23T20:16:23.772+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922290, Price 122271.2900, Quantity 0.00099, GridID: 6
2025-07-23T20:16:23.775+0700	[34mINFO[0m	bot/bot.go:581	Order 7922289 confirmed by exchange with status: NEW
2025-07-23T20:16:23.775+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922289, Price 119838.0900, Quantity 0.00099, GridID: 8
2025-07-23T20:16:23.884+0700	[34mINFO[0m	bot/bot.go:581	Order 7922291 confirmed by exchange with status: NEW
2025-07-23T20:16:23.886+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922291, Price 121048.5800, Quantity 0.00099, GridID: 7
2025-07-23T20:16:23.911+0700	[34mINFO[0m	bot/bot.go:581	Order 7922292 confirmed by exchange with status: NEW
2025-07-23T20:16:23.911+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922292, Price 124753.8900, Quantity 0.00099, GridID: 4
2025-07-23T20:16:23.911+0700	[31mERROR[0m	bot/bot.go:1479	failed to place buy order (GridID 14): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:16:23.911+0700	[31mERROR[0m	bot/bot.go:1479	failed to place buy order (GridID 11): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:16:23.911+0700	[31mERROR[0m	bot/bot.go:1479	failed to place buy order (GridID 10): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:16:23.911+0700	[31mERROR[0m	bot/bot.go:1479	failed to place buy order (GridID 12): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:16:23.911+0700	[31mERROR[0m	bot/bot.go:1479	failed to place buy order (GridID 13): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:16:23.912+0700	[31mERROR[0m	bot/bot.go:1507	--- Entering Safe Mode ---
2025-07-23T20:16:23.913+0700	[31mERROR[0m	bot/bot.go:1508	Reason: one or more orders failed during initial grid setup: failed to place buy order (GridID 14): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:16:23.913+0700	[31mERROR[0m	bot/bot.go:1509	Bot has stopped all trading activity. Manual intervention required.
2025-07-23T20:16:23.913+0700	[31mFATAL[0m	bot/main.go:251	Failed to start bot: failed to initialize grid and position: initial grid setup failed: one or more orders failed during initial grid setup: failed to place buy order (GridID 14): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:17:38.927+0700	[34mINFO[0m	bot/main.go:127	--- Starting Live Trading Mode ---
2025-07-23T20:17:38.927+0700	[34mINFO[0m	bot/main.go:149	Using Binance Spot testnet...
2025-07-23T20:17:38.927+0700	[34mINFO[0m	bot/main.go:184	Initialized Enhanced Spot trading exchange using go-binance SDK
2025-07-23T20:17:38.927+0700	[34mINFO[0m	bot/main.go:196	Initializing exchange settings...
2025-07-23T20:17:38.927+0700	[34mINFO[0m	bot/main.go:243	Spot trading mode - skipping futures-specific settings
2025-07-23T20:17:38.927+0700	[34mINFO[0m	bot/bot.go:93	Initialized spot trading mode
2025-07-23T20:17:39.232+0700	[34mINFO[0m	bot/bot.go:103	Successfully fetched and cached trading rules for BTCUSDT.
2025-07-23T20:17:39.233+0700	[34mINFO[0m	bot/bot.go:809	Starting bot...
2025-07-23T20:17:39.341+0700	[34mINFO[0m	bot/bot.go:674	Successfully obtained Listen Key: hyrt6VojLKlAfHRtdbFLEYGPorBs5wJMeL2acTslXPai5RdN5UVcWFI7QnEM
2025-07-23T20:17:39.817+0700	[34mINFO[0m	bot/bot.go:681	Successfully connected to user data stream WebSocket.
2025-07-23T20:17:39.817+0700	[34mINFO[0m	bot/bot.go:438	--- Starting new trading cycle ---
2025-07-23T20:17:39.817+0700	[34mINFO[0m	bot/bot.go:753	WebSocket message listener loop started.
2025-07-23T20:17:39.817+0700	[34mINFO[0m	bot/bot.go:1548	Core event processor started.
2025-07-23T20:17:39.917+0700	[34mINFO[0m	bot/bot.go:454	New cycle defined: Entry Price: 117991.9900, Reversion Price (Grid Top): 129791.1890
2025-07-23T20:17:39.917+0700	[34mINFO[0m	bot/bot.go:481	Successfully generated conceptual grid with 79 levels.
2025-07-23T20:17:39.917+0700	[34mINFO[0m	bot/bot.go:496	Calculated initial position quantity: 0.********
2025-07-23T20:17:39.917+0700	[33mWARN[0m	bot/bot.go:1056	Could not get account state to check exposure limit: failed to get balances: spot exchange implementation needed
2025-07-23T20:17:39.917+0700	[33mWARN[0m	bot/bot.go:499	Initial position blocked: wallet exposure limit would be exceeded.
2025-07-23T20:17:39.918+0700	[34mINFO[0m	bot/bot.go:519	Initial position confirmed, setting up grid orders...
2025-07-23T20:17:39.918+0700	[34mINFO[0m	bot/bot.go:261	Setting up LONG-optimized grid around price 117991.9900
2025-07-23T20:17:39.918+0700	[34mINFO[0m	bot/bot.go:1439	--- Setting up initial grid, center price: 117991.9900 ---
2025-07-23T20:17:39.918+0700	[34mINFO[0m	bot/bot.go:1464	Found closest pivot grid ID: 9 (Price: 118566.4800)
2025-07-23T20:17:39.918+0700	[33mWARN[0m	bot/bot.go:1056	Could not get account state to check exposure limit: failed to get balances: spot exchange implementation needed
2025-07-23T20:17:39.918+0700	[33mWARN[0m	bot/bot.go:1056	Could not get account state to check exposure limit: failed to get balances: spot exchange implementation needed
2025-07-23T20:17:39.918+0700	[33mWARN[0m	bot/bot.go:1056	Could not get account state to check exposure limit: failed to get balances: spot exchange implementation needed
2025-07-23T20:17:39.918+0700	[33mWARN[0m	bot/bot.go:1056	Could not get account state to check exposure limit: failed to get balances: spot exchange implementation needed
2025-07-23T20:17:39.918+0700	[33mWARN[0m	bot/bot.go:1056	Could not get account state to check exposure limit: failed to get balances: spot exchange implementation needed
2025-07-23T20:17:40.026+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922526, Price 122195.8200, Quantity 0.00099, GridID: 6. Waiting for confirmation...
2025-07-23T20:17:40.026+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922528, Price 124676.8900, Quantity 0.00099, GridID: 4. Waiting for confirmation...
2025-07-23T20:17:40.026+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922527, Price 119764.1300, Quantity 0.00099, GridID: 8. Waiting for confirmation...
2025-07-23T20:17:40.052+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922529, Price 123430.1200, Quantity 0.00099, GridID: 5. Waiting for confirmation...
2025-07-23T20:17:40.162+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922530, Price 120973.8600, Quantity 0.00099, GridID: 7. Waiting for confirmation...
2025-07-23T20:17:40.329+0700	[34mINFO[0m	bot/bot.go:581	Order 7922526 confirmed by exchange with status: NEW
2025-07-23T20:17:40.329+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922526, Price 122195.8200, Quantity 0.00099, GridID: 6
2025-07-23T20:17:40.335+0700	[34mINFO[0m	bot/bot.go:581	Order 7922527 confirmed by exchange with status: NEW
2025-07-23T20:17:40.335+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922527, Price 119764.1300, Quantity 0.00099, GridID: 8
2025-07-23T20:17:40.361+0700	[34mINFO[0m	bot/bot.go:581	Order 7922528 confirmed by exchange with status: NEW
2025-07-23T20:17:40.361+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922528, Price 124676.8900, Quantity 0.00099, GridID: 4
2025-07-23T20:17:40.385+0700	[34mINFO[0m	bot/bot.go:581	Order 7922529 confirmed by exchange with status: NEW
2025-07-23T20:17:40.385+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922529, Price 123430.1200, Quantity 0.00099, GridID: 5
2025-07-23T20:17:40.468+0700	[34mINFO[0m	bot/bot.go:581	Order 7922530 confirmed by exchange with status: NEW
2025-07-23T20:17:40.468+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922530, Price 120973.8600, Quantity 0.00099, GridID: 7
2025-07-23T20:17:40.468+0700	[31mERROR[0m	bot/bot.go:1511	failed to place buy order (GridID 14): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:17:40.468+0700	[31mERROR[0m	bot/bot.go:1511	failed to place buy order (GridID 11): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:17:40.468+0700	[31mERROR[0m	bot/bot.go:1511	failed to place buy order (GridID 10): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:17:40.468+0700	[31mERROR[0m	bot/bot.go:1511	failed to place buy order (GridID 12): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:17:40.468+0700	[31mERROR[0m	bot/bot.go:1511	failed to place buy order (GridID 13): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:17:40.468+0700	[31mERROR[0m	bot/bot.go:1539	--- Entering Safe Mode ---
2025-07-23T20:17:40.468+0700	[31mERROR[0m	bot/bot.go:1540	Reason: one or more orders failed during initial grid setup: failed to place buy order (GridID 14): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:17:40.469+0700	[31mERROR[0m	bot/bot.go:1541	Bot has stopped all trading activity. Manual intervention required.
2025-07-23T20:17:40.469+0700	[31mFATAL[0m	bot/main.go:251	Failed to start bot: failed to initialize grid and position: initial grid setup failed: one or more orders failed during initial grid setup: failed to place buy order (GridID 14): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:17:40.469+0700	[34mINFO[0m	bot/bot.go:913	Cancelling all active orders...
2025-07-23T20:18:28.238+0700	[34mINFO[0m	bot/main.go:127	--- Starting Live Trading Mode ---
2025-07-23T20:18:28.239+0700	[34mINFO[0m	bot/main.go:149	Using Binance Spot testnet...
2025-07-23T20:18:28.239+0700	[34mINFO[0m	bot/main.go:184	Initialized Enhanced Spot trading exchange using go-binance SDK
2025-07-23T20:18:28.239+0700	[34mINFO[0m	bot/main.go:196	Initializing exchange settings...
2025-07-23T20:18:28.239+0700	[34mINFO[0m	bot/main.go:243	Spot trading mode - skipping futures-specific settings
2025-07-23T20:18:28.239+0700	[34mINFO[0m	bot/bot.go:93	Initialized spot trading mode
2025-07-23T20:18:28.537+0700	[34mINFO[0m	bot/bot.go:103	Successfully fetched and cached trading rules for BTCUSDT.
2025-07-23T20:18:28.537+0700	[34mINFO[0m	bot/bot.go:809	Starting bot...
2025-07-23T20:18:28.637+0700	[34mINFO[0m	bot/bot.go:674	Successfully obtained Listen Key: hyrt6VojLKlAfHRtdbFLEYGPorBs5wJMeL2acTslXPai5RdN5UVcWFI7QnEM
2025-07-23T20:18:29.108+0700	[34mINFO[0m	bot/bot.go:681	Successfully connected to user data stream WebSocket.
2025-07-23T20:18:29.109+0700	[34mINFO[0m	bot/bot.go:438	--- Starting new trading cycle ---
2025-07-23T20:18:29.109+0700	[34mINFO[0m	bot/bot.go:753	WebSocket message listener loop started.
2025-07-23T20:18:29.109+0700	[34mINFO[0m	bot/bot.go:1548	Core event processor started.
2025-07-23T20:18:29.212+0700	[34mINFO[0m	bot/bot.go:454	New cycle defined: Entry Price: 117940.1400, Reversion Price (Grid Top): 129734.1540
2025-07-23T20:18:29.213+0700	[34mINFO[0m	bot/bot.go:481	Successfully generated conceptual grid with 79 levels.
2025-07-23T20:18:29.213+0700	[34mINFO[0m	bot/bot.go:496	Calculated initial position quantity: 0.********
2025-07-23T20:18:29.335+0700	[33mWARN[0m	bot/bot.go:1056	Could not get account state to check exposure limit: invalid balance data type
2025-07-23T20:18:29.335+0700	[33mWARN[0m	bot/bot.go:499	Initial position blocked: wallet exposure limit would be exceeded.
2025-07-23T20:18:29.335+0700	[34mINFO[0m	bot/bot.go:519	Initial position confirmed, setting up grid orders...
2025-07-23T20:18:29.335+0700	[34mINFO[0m	bot/bot.go:261	Setting up LONG-optimized grid around price 117940.1400
2025-07-23T20:18:29.335+0700	[34mINFO[0m	bot/bot.go:1439	--- Setting up initial grid, center price: 117940.1400 ---
2025-07-23T20:18:29.335+0700	[34mINFO[0m	bot/bot.go:1464	Found closest pivot grid ID: 9 (Price: 118514.3800)
2025-07-23T20:18:29.469+0700	[33mWARN[0m	bot/bot.go:1056	Could not get account state to check exposure limit: invalid balance data type
2025-07-23T20:18:29.469+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922636, Price 123375.8800, Quantity 0.00099, GridID: 5. Waiting for confirmation...
2025-07-23T20:18:29.470+0700	[33mWARN[0m	bot/bot.go:1056	Could not get account state to check exposure limit: invalid balance data type
2025-07-23T20:18:29.470+0700	[33mWARN[0m	bot/bot.go:1056	Could not get account state to check exposure limit: invalid balance data type
2025-07-23T20:18:29.493+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922637, Price 120920.7000, Quantity 0.00099, GridID: 7. Waiting for confirmation...
2025-07-23T20:18:29.578+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922639, Price 122142.1300, Quantity 0.00099, GridID: 6. Waiting for confirmation...
2025-07-23T20:18:29.579+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922640, Price 119711.5000, Quantity 0.00099, GridID: 8. Waiting for confirmation...
2025-07-23T20:18:29.584+0700	[33mWARN[0m	bot/bot.go:1056	Could not get account state to check exposure limit: invalid balance data type
2025-07-23T20:18:29.588+0700	[34mINFO[0m	bot/bot.go:560	Submitted SELL order: ID 7922641, Price 124622.1100, Quantity 0.00099, GridID: 4. Waiting for confirmation...
2025-07-23T20:18:29.611+0700	[33mWARN[0m	bot/bot.go:1056	Could not get account state to check exposure limit: invalid balance data type
2025-07-23T20:18:29.772+0700	[34mINFO[0m	bot/bot.go:581	Order 7922636 confirmed by exchange with status: NEW
2025-07-23T20:18:29.772+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922636, Price 123375.8800, Quantity 0.00099, GridID: 5
2025-07-23T20:18:29.798+0700	[34mINFO[0m	bot/bot.go:581	Order 7922637 confirmed by exchange with status: NEW
2025-07-23T20:18:29.799+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922637, Price 120920.7000, Quantity 0.00099, GridID: 7
2025-07-23T20:18:30.015+0700	[34mINFO[0m	bot/bot.go:581	Order 7922639 confirmed by exchange with status: NEW
2025-07-23T20:18:30.015+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922639, Price 122142.1300, Quantity 0.00099, GridID: 6
2025-07-23T20:18:30.041+0700	[34mINFO[0m	bot/bot.go:581	Order 7922640 confirmed by exchange with status: NEW
2025-07-23T20:18:30.041+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922640, Price 119711.5000, Quantity 0.00099, GridID: 8
2025-07-23T20:18:30.073+0700	[34mINFO[0m	bot/bot.go:581	Order 7922641 confirmed by exchange with status: NEW
2025-07-23T20:18:30.073+0700	[34mINFO[0m	bot/bot.go:603	Successfully confirmed SELL order: ID 7922641, Price 124622.1100, Quantity 0.00099, GridID: 4
2025-07-23T20:18:30.073+0700	[31mERROR[0m	bot/bot.go:1511	failed to place buy order (GridID 14): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:18:30.073+0700	[31mERROR[0m	bot/bot.go:1511	failed to place buy order (GridID 13): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:18:30.073+0700	[31mERROR[0m	bot/bot.go:1511	failed to place buy order (GridID 10): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:18:30.073+0700	[31mERROR[0m	bot/bot.go:1511	failed to place buy order (GridID 11): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:18:30.073+0700	[31mERROR[0m	bot/bot.go:1511	failed to place buy order (GridID 12): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:18:30.073+0700	[31mERROR[0m	bot/bot.go:1539	--- Entering Safe Mode ---
2025-07-23T20:18:30.073+0700	[31mERROR[0m	bot/bot.go:1540	Reason: one or more orders failed during initial grid setup: failed to place buy order (GridID 14): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:18:30.073+0700	[31mERROR[0m	bot/bot.go:1541	Bot has stopped all trading activity. Manual intervention required.
2025-07-23T20:18:30.073+0700	[31mFATAL[0m	bot/main.go:251	Failed to start bot: failed to initialize grid and position: initial grid setup failed: one or more orders failed during initial grid setup: failed to place buy order (GridID 14): order blocked: wallet exposure limit would be exceeded
2025-07-23T20:19:03.584+0700	[34mINFO[0m	bot/main.go:127	--- Starting Live Trading Mode ---
2025-07-23T20:19:03.584+0700	[34mINFO[0m	bot/main.go:149	Using Binance Spot testnet...
2025-07-23T20:19:03.584+0700	[34mINFO[0m	bot/main.go:184	Initialized Enhanced Spot trading exchange using go-binance SDK
2025-07-23T20:19:03.584+0700	[34mINFO[0m	bot/main.go:196	Initializing exchange settings...
2025-07-23T20:19:03.584+0700	[34mINFO[0m	bot/main.go:243	Spot trading mode - skipping futures-specific settings
2025-07-23T20:19:03.584+0700	[34mINFO[0m	bot/bot.go:93	Initialized spot trading mode
2025-07-23T20:19:03.938+0700	[34mINFO[0m	bot/bot.go:103	Successfully fetched and cached trading rules for BTCUSDT.
2025-07-23T20:19:03.938+0700	[34mINFO[0m	bot/bot.go:809	Starting bot...
2025-07-23T20:19:04.042+0700	[34mINFO[0m	bot/bot.go:674	Successfully obtained Listen Key: hyrt6VojLKlAfHRtdbFLEYGPorBs5wJMeL2acTslXPai5RdN5UVcWFI7QnEM
2025-07-23T20:19:04.548+0700	[34mINFO[0m	bot/bot.go:681	Successfully connected to user data stream WebSocket.
2025-07-23T20:19:04.548+0700	[34mINFO[0m	bot/bot.go:438	--- Starting new trading cycle ---
2025-07-23T20:19:04.548+0700	[34mINFO[0m	bot/bot.go:1548	Core event processor started.
2025-07-23T20:19:04.548+0700	[34mINFO[0m	bot/bot.go:753	WebSocket message listener loop started.
2025-07-23T20:19:04.653+0700	[34mINFO[0m	bot/bot.go:454	New cycle defined: Entry Price: 117940.1200, Reversion Price (Grid Top): 129734.1320
2025-07-23T20:19:04.653+0700	[34mINFO[0m	bot/bot.go:481	Successfully generated conceptual grid with 79 levels.
2025-07-23T20:19:04.653+0700	[34mINFO[0m	bot/bot.go:496	Calculated initial position quantity: 0.********
2025-07-23T20:19:04.968+0700	[34mINFO[0m	bot/bot.go:381	Submitted initial market buy order ID: 7922723, Quantity: 0.00990. Waiting for fill...
2025-07-23T20:19:06.099+0700	[34mINFO[0m	bot/bot.go:405	Initial position order 7922723 has been filled!
2025-07-23T20:19:06.099+0700	[31mERROR[0m	bot/bot.go:1539	--- Entering Safe Mode ---
2025-07-23T20:19:06.099+0700	[31mERROR[0m	bot/bot.go:1540	Reason: failed to initialize grid and position: failed to establish initial position, cannot continue: could not parse fill price for initial order 7922723: strconv.ParseFloat: parsing "": invalid syntax
2025-07-23T20:19:06.099+0700	[31mERROR[0m	bot/bot.go:1541	Bot has stopped all trading activity. Manual intervention required.
2025-07-23T20:19:06.099+0700	[31mFATAL[0m	bot/main.go:251	Failed to start bot: failed to initialize grid and position: failed to establish initial position, cannot continue: could not parse fill price for initial order 7922723: strconv.ParseFloat: parsing "": invalid syntax
2025-07-23T20:19:06.099+0700	[34mINFO[0m	bot/bot.go:913	Cancelling all active orders...

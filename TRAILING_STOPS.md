# Trailing Stop Functionality

This document describes the trailing stop functionality that has been added to the grid trading bot.

## Overview

The trailing stop feature allows the bot to automatically adjust stop loss and take profit levels as the market price moves favorably, helping to lock in profits while limiting losses. The bot supports two types of trailing stops:

1. **Trailing Up (Trailing Take Profit)**: Adjusts take profit levels upward as price moves favorably
2. **Trailing Down (Trailing Stop Loss)**: Adjusts stop loss levels to follow price movements and limit losses

## Configuration

Add the following parameters to your `config.json` file:

```json
{
  "enable_trailing_up": false,
  "enable_trailing_down": false,
  "trailing_up_distance": 0.02,
  "trailing_down_distance": 0.015,
  "trailing_type": "percentage"
}
```

### Configuration Parameters

- **`enable_trailing_up`** (boolean): Enable/disable trailing up functionality
- **`enable_trailing_down`** (boolean): Enable/disable trailing down functionality  
- **`trailing_up_distance`** (float): Distance for trailing up adjustments
- **`trailing_down_distance`** (float): Distance for trailing down adjustments
- **`trailing_type`** (string): Type of distance calculation
  - `"percentage"`: Distance as percentage (e.g., 0.02 = 2%)
  - `"absolute"`: Distance as absolute price units (e.g., 2.0 = $2)

## How It Works

### Trailing Up (Take Profit)

For LONG positions:
- Monitors the highest price reached since position entry
- When price moves up, adjusts the take profit level to stay below the highest price
- Only moves the take profit level higher (never lower)
- Helps lock in profits while allowing for continued upward movement

Example with 2% trailing up distance:
- Entry price: $100
- Price rises to $110 → Take profit set at $107.80 (110 * 0.98)
- Price rises to $115 → Take profit adjusted to $112.70 (115 * 0.98)
- If price falls back to $112, the take profit at $112.70 would be triggered

### Trailing Down (Stop Loss)

For LONG positions:
- Monitors price movements to limit losses
- Adjusts stop loss level to follow the lowest price reached
- Helps limit losses while allowing for potential recovery

Example with 1.5% trailing down distance:
- Entry price: $100
- Price falls to $95 → Stop loss set at $93.58 (95 * 0.985)
- Price falls to $90 → Stop loss adjusted to $88.65 (90 * 0.985)

## Integration with Grid Trading

The trailing stop functionality is seamlessly integrated with the existing grid trading strategy:

1. **Initialization**: Trailing stops are initialized when the base position is established
2. **Price Monitoring**: The bot monitors price changes every 10 seconds (configurable)
3. **Order Management**: Trailing stop orders are placed and updated automatically
4. **Cycle Management**: Trailing stops are reset when a trading cycle completes
5. **Conflict Resolution**: Trailing stop orders are recognized and handled separately from grid orders

## Logging and Monitoring

The bot provides comprehensive logging for trailing stop activities:

### Status Logging
- Current trailing stop levels
- Unrealized P&L
- Distance from current price
- Total adjustments made
- Recent adjustment activity

### Adjustment Logging
- Timestamp of each adjustment
- Trigger price that caused the adjustment
- Old and new trailing levels
- Reason for adjustment

### History Tracking
- All trailing stop adjustments are saved to JSON files
- Files are named `trailing_stop_history_YYYY-MM-DD.json`
- Includes complete state and adjustment history for analysis

## Example Log Output

```
=== Trailing Stop Status ===
Position: LONG, Entry Price: 100.0000, Current Price: 105.2000
Highest Price: 108.5000, Lowest Price: 98.2000
Unrealized P&L: 5.20%
Trailing Up Level: 106.3300 (Order ID: 12345, Distance: 2.00%)
Trailing Down Level: 96.5470 (Order ID: 12346, Distance: 1.50%)
Total Adjustments: 3, Last Update: 14:32:15
Recent adjustments (last hour): 2
```

## Testing

The implementation includes comprehensive tests covering:

- Trailing stop initialization
- Trailing up adjustments
- Trailing down adjustments  
- Percentage vs absolute distance calculations
- Integration with grid trading logic

Run tests with:
```bash
go test -v ./internal/bot -run TestTrailing
```

## Best Practices

1. **Conservative Settings**: Start with conservative trailing distances (1-3%)
2. **Monitor Performance**: Review trailing stop history files to analyze effectiveness
3. **Market Conditions**: Consider disabling trailing stops in highly volatile markets
4. **Risk Management**: Trailing stops complement but don't replace proper position sizing
5. **Testing**: Test thoroughly in paper trading mode before using with real funds

## Limitations

- Trailing stops work best in trending markets
- May result in premature exits in choppy/sideways markets
- Requires stable internet connection for real-time price monitoring
- Exchange rate limits may affect order update frequency

## Future Enhancements

Potential improvements for future versions:
- Support for SHORT positions
- Advanced trailing algorithms (e.g., ATR-based trailing)
- Conditional trailing stops based on technical indicators
- Integration with volatility-based distance adjustments
- Support for multiple timeframe analysis

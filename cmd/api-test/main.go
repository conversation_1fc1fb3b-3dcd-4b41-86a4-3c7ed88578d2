package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		fmt.Printf("Warning: Could not load .env file: %v\n", err)
	}

	apiKey := os.Getenv("BINANCE_API_KEY")
	secretKey := os.Getenv("BINANCE_SECRET_KEY")

	if apiKey == "" || secretKey == "" {
		fmt.Println("Error: BINANCE_API_KEY and BINANCE_SECRET_KEY must be set")
		os.Exit(1)
	}

	fmt.Println("🔑 Testing Binance API Credentials...")
	fmt.Printf("API Key: %s...%s\n", api<PERSON>ey[:8], api<PERSON>ey[len(apiKey)-8:])

	// Test 1: Check server time (no authentication required)
	fmt.Println("\n📡 Test 1: Server Time (No Auth)")
	testServerTime()

	// Test 2: Test spot account info
	fmt.Println("\n💰 Test 2: Spot Account Info")
	testSpotAccount(apiKey, secretKey)

	// Test 3: Test futures account info
	fmt.Println("\n🚀 Test 3: Futures Account Info")
	testFuturesAccount(apiKey, secretKey)

	// Test 4: Test DMCUSDT symbol info
	fmt.Println("\n📊 Test 4: DMCUSDT Symbol Info")
	testSymbolInfo()
}

func testServerTime() {
	resp, err := http.Get("https://api.binance.com/api/v3/time")
	if err != nil {
		fmt.Printf("❌ Error: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Error reading response: %v\n", err)
		return
	}

	fmt.Printf("✅ Status: %d\n", resp.StatusCode)
	fmt.Printf("Response: %s\n", string(body))
}

func testSpotAccount(apiKey, secretKey string) {
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
	queryString := "timestamp=" + timestamp

	// Create signature
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(queryString))
	signature := hex.EncodeToString(h.Sum(nil))

	url := "https://api.binance.com/api/v3/account?" + queryString + "&signature=" + signature

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Printf("❌ Error creating request: %v\n", err)
		return
	}

	req.Header.Set("X-MBX-APIKEY", apiKey)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ Error: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Error reading response: %v\n", err)
		return
	}

	fmt.Printf("Status: %d\n", resp.StatusCode)
	if resp.StatusCode == 200 {
		fmt.Printf("✅ Spot API access: OK\n")
	} else {
		fmt.Printf("❌ Spot API access: FAILED\n")
		fmt.Printf("Response: %s\n", string(body))
	}
}

func testFuturesAccount(apiKey, secretKey string) {
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
	queryString := "timestamp=" + timestamp

	// Create signature
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(queryString))
	signature := hex.EncodeToString(h.Sum(nil))

	url := "https://fapi.binance.com/fapi/v2/account?" + queryString + "&signature=" + signature

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Printf("❌ Error creating request: %v\n", err)
		return
	}

	req.Header.Set("X-MBX-APIKEY", apiKey)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ Error: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Error reading response: %v\n", err)
		return
	}

	fmt.Printf("Status: %d\n", resp.StatusCode)
	if resp.StatusCode == 200 {
		fmt.Printf("✅ Futures API access: OK\n")
	} else {
		fmt.Printf("❌ Futures API access: FAILED\n")
		fmt.Printf("Response: %s\n", string(body))
	}
}

func testSymbolInfo() {
	// Test DMCUSDT futures symbol
	resp, err := http.Get("https://fapi.binance.com/fapi/v1/exchangeInfo")
	if err != nil {
		fmt.Printf("❌ Error: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Error reading response: %v\n", err)
		return
	}

	fmt.Printf("✅ Status: %d\n", resp.StatusCode)
	
	// Check if DMCUSDT is mentioned in the response
	bodyStr := string(body)
	if len(bodyStr) > 1000 {
		fmt.Printf("Response length: %d characters\n", len(bodyStr))
		if contains := fmt.Sprintf("%v", bodyStr); len(contains) > 0 {
			fmt.Printf("✅ Exchange info retrieved successfully\n")
		}
	}
}

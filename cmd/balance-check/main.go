package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

type FuturesBalance struct {
	AccountAlias       string `json:"accountAlias"`
	Asset              string `json:"asset"`
	Balance            string `json:"balance"`
	CrossWalletBalance string `json:"crossWalletBalance"`
	CrossUnPnl         string `json:"crossUnPnl"`
	AvailableBalance   string `json:"availableBalance"`
	MaxWithdrawAmount  string `json:"maxWithdrawAmount"`
	MarginAvailable    bool   `json:"marginAvailable"`
	UpdateTime         int64  `json:"updateTime"`
}

type FuturesAccountInfo struct {
	FeeTier                     int               `json:"feeTier"`
	CanTrade                    bool              `json:"canTrade"`
	CanDeposit                  bool              `json:"canDeposit"`
	CanWithdraw                 bool              `json:"canWithdraw"`
	UpdateTime                  int64             `json:"updateTime"`
	TotalInitialMargin          string            `json:"totalInitialMargin"`
	TotalMaintMargin            string            `json:"totalMaintMargin"`
	TotalWalletBalance          string            `json:"totalWalletBalance"`
	TotalUnrealizedProfit       string            `json:"totalUnrealizedProfit"`
	TotalMarginBalance          string            `json:"totalMarginBalance"`
	TotalPositionInitialMargin  string            `json:"totalPositionInitialMargin"`
	TotalOpenOrderInitialMargin string            `json:"totalOpenOrderInitialMargin"`
	TotalCrossWalletBalance     string            `json:"totalCrossWalletBalance"`
	TotalCrossUnPnl             string            `json:"totalCrossUnPnl"`
	AvailableBalance            string            `json:"availableBalance"`
	MaxWithdrawAmount           string            `json:"maxWithdrawAmount"`
	Assets                      []FuturesBalance  `json:"assets"`
}

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		fmt.Printf("Warning: Could not load .env file: %v\n", err)
	}

	apiKey := os.Getenv("BINANCE_API_KEY")
	secretKey := os.Getenv("BINANCE_SECRET_KEY")

	if apiKey == "" || secretKey == "" {
		fmt.Println("Error: BINANCE_API_KEY and BINANCE_SECRET_KEY must be set")
		os.Exit(1)
	}

	fmt.Println("💰 Checking Binance Futures Account Balance...")
	
	// Get futures account info
	accountInfo, err := getFuturesAccountInfo(apiKey, secretKey)
	if err != nil {
		fmt.Printf("❌ Error getting account info: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ Account Status: Can Trade: %v, Can Deposit: %v, Can Withdraw: %v\n", 
		accountInfo.CanTrade, accountInfo.CanDeposit, accountInfo.CanWithdraw)
	
	fmt.Printf("📊 Total Wallet Balance: %s USDT\n", accountInfo.TotalWalletBalance)
	fmt.Printf("💵 Available Balance: %s USDT\n", accountInfo.AvailableBalance)
	fmt.Printf("📈 Total Unrealized Profit: %s USDT\n", accountInfo.TotalUnrealizedProfit)
	fmt.Printf("🔒 Total Initial Margin: %s USDT\n", accountInfo.TotalInitialMargin)

	fmt.Println("\n💎 Asset Balances:")
	for _, asset := range accountInfo.Assets {
		if asset.Balance != "0" && asset.Balance != "0.********" {
			fmt.Printf("   %s: Balance: %s, Available: %s\n", 
				asset.Asset, asset.Balance, asset.AvailableBalance)
		}
	}

	// Check USDT specifically
	for _, asset := range accountInfo.Assets {
		if asset.Asset == "USDT" {
			fmt.Printf("\n💰 USDT Details:\n")
			fmt.Printf("   Balance: %s USDT\n", asset.Balance)
			fmt.Printf("   Available: %s USDT\n", asset.AvailableBalance)
			fmt.Printf("   Cross Wallet Balance: %s USDT\n", asset.CrossWalletBalance)
			break
		}
	}
}

func getFuturesAccountInfo(apiKey, secretKey string) (*FuturesAccountInfo, error) {
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
	queryString := "timestamp=" + timestamp

	// Create signature
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(queryString))
	signature := hex.EncodeToString(h.Sum(nil))

	url := "https://fapi.binance.com/fapi/v2/account?" + queryString + "&signature=" + signature

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	req.Header.Set("X-MBX-APIKEY", apiKey)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %v", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %v", err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body))
	}

	var accountInfo FuturesAccountInfo
	if err := json.Unmarshal(body, &accountInfo); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	return &accountInfo, nil
}

package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"
)

func main() {
	var (
		reportType = flag.String("type", "current", "Report type: current, detailed, history")
		symbol     = flag.String("symbol", "BTCUSDT", "Trading symbol")
		format     = flag.String("format", "console", "Output format: console, json, file")
		logDir     = flag.String("logdir", "logs", "Log directory path")
	)
	flag.Parse()

	fmt.Println("📊 Grid Trading Bot - P&L Report Tool")
	fmt.Println("=" + strings.Repeat("=", 50))

	switch *reportType {
	case "current":
		showCurrentReport(*symbol, *logDir, *format)
	case "detailed":
		showDetailedReport(*symbol, *logDir, *format)
	case "history":
		showHistoryReport(*symbol, *logDir, *format)
	default:
		fmt.Printf("Unknown report type: %s\n", *reportType)
		fmt.Println("Available types: current, detailed, history")
		os.Exit(1)
	}
}

func showCurrentReport(symbol, logDir, format string) {
	fmt.Printf("\n🔍 Searching for current P&L reports for %s...\n", symbol)
	
	// Find the most recent P&L report file
	reportFile, err := findLatestPnLReport(logDir, symbol)
	if err != nil {
		log.Fatalf("Failed to find P&L report: %v", err)
	}
	
	if reportFile == "" {
		fmt.Println("❌ No P&L reports found. Make sure the bot is running with P&L tracking enabled.")
		return
	}
	
	fmt.Printf("📄 Found report: %s\n", reportFile)
	
	// Load and display the report
	data, err := ioutil.ReadFile(reportFile)
	if err != nil {
		log.Fatalf("Failed to read report file: %v", err)
	}
	
	var report map[string]interface{}
	if err := json.Unmarshal(data, &report); err != nil {
		log.Fatalf("Failed to parse report file: %v", err)
	}
	
	// Display based on format
	switch format {
	case "console":
		displayCurrentReportConsole(report)
	case "json":
		displayCurrentReportJSON(report)
	case "file":
		saveCurrentReportToFile(report, symbol)
	default:
		fmt.Printf("Unknown format: %s\n", format)
	}
}

func showDetailedReport(symbol, logDir, format string) {
	fmt.Printf("\n📋 Generating detailed P&L report for %s...\n", symbol)
	
	reportFile, err := findLatestPnLReport(logDir, symbol)
	if err != nil {
		log.Fatalf("Failed to find P&L report: %v", err)
	}
	
	if reportFile == "" {
		fmt.Println("❌ No P&L reports found.")
		return
	}
	
	data, err := ioutil.ReadFile(reportFile)
	if err != nil {
		log.Fatalf("Failed to read report file: %v", err)
	}
	
	var report map[string]interface{}
	if err := json.Unmarshal(data, &report); err != nil {
		log.Fatalf("Failed to parse report file: %v", err)
	}
	
	displayDetailedReport(report, format)
}

func showHistoryReport(symbol, logDir, format string) {
	fmt.Printf("\n📈 Generating P&L history report for %s...\n", symbol)
	
	// Find all P&L report files
	files, err := findAllPnLReports(logDir, symbol)
	if err != nil {
		log.Fatalf("Failed to find P&L reports: %v", err)
	}
	
	if len(files) == 0 {
		fmt.Println("❌ No P&L reports found.")
		return
	}
	
	fmt.Printf("📄 Found %d report files\n", len(files))
	displayHistoryReport(files, format)
}

func findLatestPnLReport(logDir, symbol string) (string, error) {
	pattern := filepath.Join(logDir, fmt.Sprintf("pnl_report_%s_*.json", symbol))
	files, err := filepath.Glob(pattern)
	if err != nil {
		return "", err
	}
	
	if len(files) == 0 {
		return "", nil
	}
	
	// Sort by modification time (newest first)
	sort.Slice(files, func(i, j int) bool {
		infoI, _ := os.Stat(files[i])
		infoJ, _ := os.Stat(files[j])
		return infoI.ModTime().After(infoJ.ModTime())
	})
	
	return files[0], nil
}

func findAllPnLReports(logDir, symbol string) ([]string, error) {
	pattern := filepath.Join(logDir, fmt.Sprintf("pnl_report_%s_*.json", symbol))
	files, err := filepath.Glob(pattern)
	if err != nil {
		return nil, err
	}
	
	// Sort by modification time (newest first)
	sort.Slice(files, func(i, j int) bool {
		infoI, _ := os.Stat(files[i])
		infoJ, _ := os.Stat(files[j])
		return infoI.ModTime().After(infoJ.ModTime())
	})
	
	return files, nil
}

func displayCurrentReportConsole(report map[string]interface{}) {
	summary, ok := report["summary"].(map[string]interface{})
	if !ok {
		fmt.Println("❌ Invalid report format")
		return
	}
	
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("📊 CURRENT P&L SUMMARY")
	fmt.Println(strings.Repeat("=", 80))
	
	// Extract values with type checking
	currentPrice := getFloat(summary, "current_price")
	baseHoldings := getFloat(summary, "base_holdings")
	quoteHoldings := getFloat(summary, "quote_holdings")
	portfolioValue := getFloat(summary, "portfolio_value")
	unrealizedPnL := getFloat(summary, "unrealized_pnl")
	realizedPnL := getFloat(summary, "realized_pnl")
	totalFees := getFloat(summary, "total_fees")
	netPnL := getFloat(summary, "net_pnl")
	pnlPercentage := getFloat(summary, "pnl_percentage")
	completedTrades := getInt(summary, "completed_trades")
	activeOrders := getInt(summary, "active_orders")
	
	fmt.Printf("💰 Current Price:      $%.2f\n", currentPrice)
	fmt.Printf("🏦 Portfolio Value:    $%.2f\n", portfolioValue)
	fmt.Printf("📈 Net P&L:            $%+.4f (%+.2f%%)\n", netPnL, pnlPercentage)
	fmt.Printf("💎 Unrealized P&L:     $%+.4f\n", unrealizedPnL)
	fmt.Printf("💰 Realized P&L:       $%+.4f\n", realizedPnL)
	fmt.Printf("💸 Total Fees:         $%.4f\n", totalFees)
	fmt.Printf("🔄 Completed Trades:   %d\n", completedTrades)
	fmt.Printf("📋 Active Orders:      %d\n", activeOrders)
	
	// Holdings breakdown
	fmt.Printf("\n🏦 HOLDINGS BREAKDOWN:\n")
	fmt.Printf("   BTC Holdings:       %.8f\n", baseHoldings)
	fmt.Printf("   USDT Balance:       %.2f\n", quoteHoldings)
	
	timestamp := getString(summary, "timestamp")
	fmt.Printf("\n⏰ Last Updated:       %s\n", timestamp)
	fmt.Println(strings.Repeat("=", 80))
}

func displayCurrentReportJSON(report map[string]interface{}) {
	jsonData, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal JSON: %v", err)
	}
	fmt.Println(string(jsonData))
}

func saveCurrentReportToFile(report map[string]interface{}, symbol string) {
	filename := fmt.Sprintf("pnl_summary_%s_%s.json", symbol, time.Now().Format("20060102_150405"))
	
	jsonData, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal JSON: %v", err)
	}
	
	if err := ioutil.WriteFile(filename, jsonData, 0644); err != nil {
		log.Fatalf("Failed to write file: %v", err)
	}
	
	fmt.Printf("✅ Report saved to: %s\n", filename)
}

func displayDetailedReport(report map[string]interface{}, format string) {
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("📋 DETAILED P&L REPORT")
	fmt.Println(strings.Repeat("=", 80))
	
	// Display all sections
	sections := []string{"summary", "initial_state", "current_state", "performance_metrics"}
	
	for _, section := range sections {
		if data, ok := report[section].(map[string]interface{}); ok {
			fmt.Printf("\n📊 %s:\n", strings.ToUpper(strings.Replace(section, "_", " ", -1)))
			for key, value := range data {
				fmt.Printf("   %s: %v\n", formatKey(key), value)
			}
		}
	}
	
	// Display completed trades
	if trades, ok := report["completed_trades"].([]interface{}); ok && len(trades) > 0 {
		fmt.Printf("\n🔄 COMPLETED TRADES (%d):\n", len(trades))
		for i, trade := range trades {
			if tradeMap, ok := trade.(map[string]interface{}); ok {
				fmt.Printf("   Trade %d: Buy $%.2f -> Sell $%.2f, P&L: $%+.4f\n", 
					i+1, 
					getFloat(tradeMap, "buy_price"),
					getFloat(tradeMap, "sell_price"),
					getFloat(tradeMap, "net_pnl"))
			}
		}
	}
	
	fmt.Println(strings.Repeat("=", 80))
}

func displayHistoryReport(files []string, format string) {
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("📈 P&L HISTORY REPORT")
	fmt.Println(strings.Repeat("=", 80))
	
	for i, file := range files {
		info, err := os.Stat(file)
		if err != nil {
			continue
		}
		
		fmt.Printf("%d. %s (Modified: %s)\n", i+1, filepath.Base(file), info.ModTime().Format("2006-01-02 15:04:05"))
		
		// Show brief summary from each file
		data, err := ioutil.ReadFile(file)
		if err != nil {
			continue
		}
		
		var report map[string]interface{}
		if err := json.Unmarshal(data, &report); err != nil {
			continue
		}
		
		if summary, ok := report["summary"].(map[string]interface{}); ok {
			netPnL := getFloat(summary, "net_pnl")
			pnlPercentage := getFloat(summary, "pnl_percentage")
			completedTrades := getInt(summary, "completed_trades")
			
			fmt.Printf("   Net P&L: $%+.4f (%+.2f%%), Trades: %d\n", netPnL, pnlPercentage, completedTrades)
		}
		fmt.Println()
	}
	
	fmt.Println(strings.Repeat("=", 80))
}

// Helper functions for type-safe value extraction
func getFloat(m map[string]interface{}, key string) float64 {
	if val, ok := m[key]; ok {
		if f, ok := val.(float64); ok {
			return f
		}
	}
	return 0.0
}

func getInt(m map[string]interface{}, key string) int {
	if val, ok := m[key]; ok {
		if f, ok := val.(float64); ok {
			return int(f)
		}
		if i, ok := val.(int); ok {
			return i
		}
	}
	return 0
}

func getString(m map[string]interface{}, key string) string {
	if val, ok := m[key]; ok {
		if s, ok := val.(string); ok {
			return s
		}
	}
	return ""
}

func formatKey(key string) string {
	// Convert snake_case to Title Case
	parts := strings.Split(key, "_")
	for i, part := range parts {
		if len(part) > 0 {
			parts[i] = strings.ToUpper(part[:1]) + part[1:]
		}
	}
	return strings.Join(parts, " ")
}

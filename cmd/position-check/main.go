package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

type FuturesPosition struct {
	Symbol                 string `json:"symbol"`
	PositionAmt            string `json:"positionAmt"`
	EntryPrice             string `json:"entryPrice"`
	MarkPrice              string `json:"markPrice"`
	UnRealizedProfit       string `json:"unRealizedProfit"`
	LiquidationPrice       string `json:"liquidationPrice"`
	Leverage               string `json:"leverage"`
	MaxNotionalValue       string `json:"maxNotionalValue"`
	MarginType             string `json:"marginType"`
	IsolatedMargin         string `json:"isolatedMargin"`
	IsAutoAddMargin        string `json:"isAutoAddMargin"`
	PositionSide           string `json:"positionSide"`
	Notional               string `json:"notional"`
	IsolatedWallet         string `json:"isolatedWallet"`
	UpdateTime             int64  `json:"updateTime"`
	BidNotional            string `json:"bidNotional"`
	AskNotional            string `json:"askNotional"`
}

type FuturesOrder struct {
	OrderId       int64  `json:"orderId"`
	Symbol        string `json:"symbol"`
	Status        string `json:"status"`
	ClientOrderId string `json:"clientOrderId"`
	Price         string `json:"price"`
	AvgPrice      string `json:"avgPrice"`
	OrigQty       string `json:"origQty"`
	ExecutedQty   string `json:"executedQty"`
	CumQuote      string `json:"cumQuote"`
	TimeInForce   string `json:"timeInForce"`
	Type          string `json:"type"`
	ReduceOnly    bool   `json:"reduceOnly"`
	ClosePosition bool   `json:"closePosition"`
	Side          string `json:"side"`
	PositionSide  string `json:"positionSide"`
	StopPrice     string `json:"stopPrice"`
	WorkingType   string `json:"workingType"`
	PriceProtect  bool   `json:"priceProtect"`
	OrigType      string `json:"origType"`
	Time          int64  `json:"time"`
	UpdateTime    int64  `json:"updateTime"`
}

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		fmt.Printf("Warning: Could not load .env file: %v\n", err)
	}

	apiKey := os.Getenv("BINANCE_API_KEY")
	secretKey := os.Getenv("BINANCE_SECRET_KEY")

	if apiKey == "" || secretKey == "" {
		fmt.Println("Error: BINANCE_API_KEY and BINANCE_SECRET_KEY must be set")
		os.Exit(1)
	}

	fmt.Println("📊 Checking DMCUSDT Futures Position and Orders...")
	
	// Check positions
	fmt.Println("\n🏦 Current Positions:")
	positions, err := getFuturesPositions(apiKey, secretKey)
	if err != nil {
		fmt.Printf("❌ Error getting positions: %v\n", err)
	} else {
		dmcFound := false
		for _, pos := range positions {
			if pos.Symbol == "DMCUSDT" && pos.PositionAmt != "0" {
				dmcFound = true
				fmt.Printf("✅ DMCUSDT Position Found:\n")
				fmt.Printf("   Position Amount: %s DMC\n", pos.PositionAmt)
				fmt.Printf("   Entry Price: %s USDT\n", pos.EntryPrice)
				fmt.Printf("   Mark Price: %s USDT\n", pos.MarkPrice)
				fmt.Printf("   Unrealized PnL: %s USDT\n", pos.UnRealizedProfit)
				fmt.Printf("   Leverage: %sx\n", pos.Leverage)
				fmt.Printf("   Margin Type: %s\n", pos.MarginType)
				fmt.Printf("   Position Side: %s\n", pos.PositionSide)
			}
		}
		if !dmcFound {
			fmt.Println("❌ No DMCUSDT position found")
		}
	}

	// Check open orders
	fmt.Println("\n📋 Open Orders:")
	orders, err := getFuturesOpenOrders(apiKey, secretKey, "DMCUSDT")
	if err != nil {
		fmt.Printf("❌ Error getting orders: %v\n", err)
	} else {
		if len(orders) == 0 {
			fmt.Println("❌ No open orders found for DMCUSDT")
		} else {
			fmt.Printf("✅ Found %d open orders for DMCUSDT:\n", len(orders))
			for i, order := range orders {
				fmt.Printf("   %d. Order ID: %d, Side: %s, Price: %s, Quantity: %s, Status: %s\n", 
					i+1, order.OrderId, order.Side, order.Price, order.OrigQty, order.Status)
			}
		}
	}

	// Get current price
	fmt.Println("\n💰 Current Market Price:")
	price, err := getCurrentPrice("DMCUSDT")
	if err != nil {
		fmt.Printf("❌ Error getting price: %v\n", err)
	} else {
		fmt.Printf("✅ DMCUSDT Current Price: %s USDT\n", price)
	}
}

func getFuturesPositions(apiKey, secretKey string) ([]FuturesPosition, error) {
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
	queryString := "timestamp=" + timestamp

	// Create signature
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(queryString))
	signature := hex.EncodeToString(h.Sum(nil))

	url := "https://fapi.binance.com/fapi/v2/positionRisk?" + queryString + "&signature=" + signature

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	req.Header.Set("X-MBX-APIKEY", apiKey)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %v", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %v", err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body))
	}

	var positions []FuturesPosition
	if err := json.Unmarshal(body, &positions); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	return positions, nil
}

func getFuturesOpenOrders(apiKey, secretKey, symbol string) ([]FuturesOrder, error) {
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
	queryString := "symbol=" + symbol + "&timestamp=" + timestamp

	// Create signature
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(queryString))
	signature := hex.EncodeToString(h.Sum(nil))

	url := "https://fapi.binance.com/fapi/v1/openOrders?" + queryString + "&signature=" + signature

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	req.Header.Set("X-MBX-APIKEY", apiKey)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %v", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %v", err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body))
	}

	var orders []FuturesOrder
	if err := json.Unmarshal(body, &orders); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	return orders, nil
}

func getCurrentPrice(symbol string) (string, error) {
	resp, err := http.Get("https://fapi.binance.com/fapi/v1/ticker/price?symbol=" + symbol)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	var priceData struct {
		Symbol string `json:"symbol"`
		Price  string `json:"price"`
	}

	if err := json.Unmarshal(body, &priceData); err != nil {
		return "", err
	}

	return priceData.Price, nil
}

# Release Workflow - Build and Release Cross-Platform Binaries

name: Release

# Trigger on release events or tag pushes
on:
  release:
    types: [published]
  push:
    tags:
      - 'v*'
      - 'release-*'

permissions:
  contents: write

jobs:
  build:
    name: Build Cross-Platform Binaries
    runs-on: ubuntu-latest

    strategy:
      matrix:
        include:
          # macOS builds
          - goos: darwin
            goarch: amd64
            asset_name: "grid-bot-darwin-amd64"
          - goos: darwin
            goarch: arm64
            asset_name: "grid-bot-darwin-arm64"
          # Linux builds
          - goos: linux
            goarch: amd64
            asset_name: "grid-bot-linux-amd64"
          # Windows builds
          - goos: windows
            goarch: amd64
            asset_name: "grid-bot-windows-amd64.exe"

    steps:
      # Checkout the repository
      - name: Checkout code
        uses: actions/checkout@v4

      # Set up Go environment
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.23'

      # Cache Go modules to speed up builds
      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      # Install dependencies
      - name: Install dependencies
        run: go mod download

      # Build the binary for the target platform
      - name: Build binary for ${{ matrix.goos }}/${{ matrix.goarch }}
        env:
          GOOS: ${{ matrix.goos }}
          GOARCH: ${{ matrix.goarch }}
          CGO_ENABLED: 0
        run: |
          go build \
            -a \
            -installsuffix cgo \
            -ldflags="-w -s -X main.version=${{ github.ref_name }} -X main.buildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
            -o ${{ matrix.asset_name }} \
            ./cmd/bot

      # Upload artifact for the release job
      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.asset_name }}
          path: ${{ matrix.asset_name }}
          retention-days: 1

  release:
    name: Create Release
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')

    steps:
      # Download all build artifacts
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: ./binaries

      # List downloaded files for debugging
      - name: List downloaded files
        run: |
          find ./binaries -type f -exec ls -la {} \;

      # Flatten the directory structure
      - name: Prepare binaries
        run: |
          mkdir -p ./release-binaries
          find ./binaries -name "grid-bot-*" -exec cp {} ./release-binaries/ \;
          ls -la ./release-binaries/

      # Create release and upload all binaries
      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          files: ./release-binaries/*
          draft: false
          prerelease: false
          generate_release_notes: true
          make_latest: true
